import React from 'react';
import { useEvaluacionesWithFilters } from '../../hooks/useEvaluations';
import { useRealtimeEvaluationsSubscription } from '../../hooks/useRealtimeEvaluations';
import { useToastContext } from '../../hooks/useToastContext';
import { EvaluationsTable } from './EvaluationsTable';
import { EvaluationsFilters } from './EvaluationsFilters';
import { BatchActionsBar } from './BatchActionsBar';
import { Pagination } from '../UI/Pagination';
import { EvaluacionListItem, EstadoEvaluacion, EvaluacionSortBy, EvaluacionGroupBy } from '../../types/evaluaciones';

interface EvaluationsListProps {
  onSelectEvaluation?: (evaluacion: EvaluacionListItem) => void;
}

export const EvaluationsList: React.FC<EvaluationsListProps> = ({
  onSelectEvaluation
}) => {
  // Activar suscripciones en tiempo real
  useRealtimeEvaluationsSubscription();

  // Toast notifications
  const toast = useToastContext();

  const {
    evaluaciones,
    total,
    totalPages,
    isLoading,
    isError,
    error,
    filters,
    updateFilters,
    handleBatchUpdate,
    selectedIds,
    toggleSelection,
    selectAll,
    clearSelection
  } = useEvaluacionesWithFilters();

  const handleFilterChange = (key: string, value: string | string[] | number | boolean | null | undefined) => {
    updateFilters({ [key]: value, page: 1 });
  };

  const handleClearFilters = () => {
    updateFilters({
      page: 1,
      page_size: 20,
      sort_by: EvaluacionSortBy.FECHA_DESC,
      group_by: EvaluacionGroupBy.NINGUNO,
      estado: [],
      agente_id: [],
      puntuacion_min: undefined,
      puntuacion_max: undefined,
      fecha_desde: undefined,
      fecha_hasta: undefined,
      // solo_con_sugerencias: false, // TODO: Agregar cuando esté disponible en el tipo
      // workflow_tags: [] // TODO: Agregar cuando esté disponible en el tipo
    });
  };

  const handleRowClick = (evaluacion: EvaluacionListItem) => {
    if (onSelectEvaluation) {
      onSelectEvaluation(evaluacion);
    }
  };

  const handleSortChange = (sortBy: EvaluacionSortBy) => {
    updateFilters({ sort_by: sortBy });
  };

  const handleGroupChange = (groupBy: EvaluacionGroupBy) => {
    updateFilters({ group_by: groupBy });
  };

  const handleSelectionChange = (ids: number[]) => {
    // Simplificar lógica de selección
    if (ids.length === 0) {
      clearSelection();
    } else if (ids.length === evaluaciones.length && evaluaciones.length > 0) {
      selectAll();
    } else {
      // Manejar cambios de selección de forma más eficiente
      const currentSet = new Set(selectedIds);
      const newSet = new Set(ids);

      // Agregar nuevas selecciones
      ids.forEach(id => {
        if (!currentSet.has(id)) {
          toggleSelection(id);
        }
      });

      // Remover deselecciones
      selectedIds.forEach(id => {
        if (!newSet.has(id)) {
          toggleSelection(id);
        }
      });
    }
  };

  const handleBatchUpdateWrapper = async (estado: EstadoEvaluacion) => {
    try {
      await handleBatchUpdate(estado);
      toast.success(
        'Evaluaciones actualizadas',
        `Se actualizaron ${selectedIds.length} evaluación(es) correctamente`
      );
    } catch (error) {
      console.error('Error updating evaluations:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      toast.error(
        'Error al actualizar evaluaciones',
        `No se pudieron actualizar las evaluaciones: ${errorMessage}`
      );
    }
  };

  if (isError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">Error al cargar evaluaciones</h3>
        <p className="text-red-600 text-sm mt-1">
          {error?.message || 'Ha ocurrido un error inesperado'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filtros */}
      <EvaluationsFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
      />

      {/* Tabla de evaluaciones */}
      <EvaluationsTable
        evaluaciones={evaluaciones}
        selectedIds={selectedIds}
        onSelectionChange={handleSelectionChange}
        onRowClick={handleRowClick}
        sortBy={filters.sort_by as EvaluacionSortBy}
        onSortChange={handleSortChange}
        groupBy={filters.group_by as EvaluacionGroupBy}
        onGroupChange={handleGroupChange}
        isLoading={isLoading}
      />

      {/* Paginación */}
      {!isLoading && evaluaciones.length > 0 && totalPages > 1 && (
        <Pagination
          currentPage={filters.page as number}
          totalPages={totalPages}
          onPageChange={(page: number) => updateFilters({ page })}
          totalItems={total}
          itemsPerPage={filters.page_size as number}
        />
      )}

      {/* Estado vacío */}
      {!isLoading && evaluaciones.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No se encontraron evaluaciones</p>
          <p className="text-gray-400 text-sm mt-2">
            Intenta ajustar los filtros de búsqueda
          </p>
        </div>
      )}

      {/* Barra de acciones masivas */}
      <BatchActionsBar
        selectedCount={selectedIds.length}
        onBatchUpdate={handleBatchUpdateWrapper}
        onClearSelection={clearSelection}
        isLoading={isLoading}
      />
    </div>
  );
};
