import React, { useState } from 'react';
import { useMejorasWithFilters, useApplyMejora, useMejora, useDiscardMejora } from '../../hooks/useImprovements';
import { useRealtimeEvaluationsSubscription } from '../../hooks/useRealtimeEvaluations';
import { UnifiedFilters } from './UnifiedFilters';
import { ImprovementsListView } from './ImprovementsListView';
import { FullScreenDiffViewer } from './FullScreenDiffViewer';
import { CheckCircle } from 'lucide-react';

interface EvaluationsImprovementsProps {
  onSelectMejora?: (mejoraId: string) => void;
}

export const EvaluationsImprovements: React.FC<EvaluationsImprovementsProps> = ({
  onSelectMejora
}) => {
  // Activar suscripciones en tiempo real
  useRealtimeEvaluationsSubscription();

  const {
    mejoras,
    total,
    totalPages,
    isLoading,
    isError,
    error,
    filters,
    updateFilters
  } = useMejorasWithFilters();

  const { mutate: applyMejora, isPending: isApplying } = useApplyMejora();
  const { mutate: discardMejora, isPending: isDiscarding } = useDiscardMejora();
  const [selectedMejoras, setSelectedMejoras] = useState<string[]>([]);
  const [selectedMejoraId, setSelectedMejoraId] = useState<string | null>(null);
  const [showDiffViewer, setShowDiffViewer] = useState(false);
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [groupBy, setGroupBy] = useState<'workflow' | 'agente' | 'none'>('none');

  // Hook para obtener detalles de la mejora seleccionada
  const { data: selectedMejoraDetail } = useMejora(selectedMejoraId || '');

  const handleFilterChange = (key: string, value: any) => {
    updateFilters({ [key]: value });
  };

  const handleClearFilters = () => {
    updateFilters({
      estado: [],
      agente_id: [],
      fecha_desde: undefined,
      fecha_hasta: undefined
    });
  };

  const handleSelectMejora = (mejoraId: string) => {
    setSelectedMejoras(prev =>
      prev.includes(mejoraId)
        ? prev.filter(id => id !== mejoraId)
        : [...prev, mejoraId]
    );
  };

  const handleApplySelected = async () => {
    if (selectedMejoras.length === 0) return;

    try {
      for (const mejoraId of selectedMejoras) {
        await applyMejora(mejoraId);
      }
      setSelectedMejoras([]);
    } catch (error) {
      console.error('Error applying mejoras:', error);
    }
  };

  const handleViewDetails = (mejoraId: string) => {
    setSelectedMejoraId(mejoraId);
    setShowDiffViewer(true);
    onSelectMejora?.(mejoraId);
  };

  const handleCloseDiffViewer = () => {
    setShowDiffViewer(false);
    setSelectedMejoraId(null);
  };

  const handleApplyMejora = async (mejoraId: string) => {
    try {
      await applyMejora(mejoraId);
    } catch (error) {
      console.error('Error applying mejora:', error);
    }
  };

  const handleDiscardMejora = async (mejoraId: string) => {
    try {
      await discardMejora(mejoraId);
    } catch (error) {
      console.error('Error discarding mejora:', error);
    }
  };

  const handleSort = (column: string) => {
    const newDirection = sortBy === column ? (sortDirection === 'asc' ? 'desc' : 'asc') : 'asc';
    setSortBy(column);
    setSortDirection(newDirection);

    // Update filters to trigger backend sorting
    updateFilters({
      sort_by: `${column}_${newDirection}` as any // Cast to match expected type
    });
  };



  if (isError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="text-red-800">Error al cargar mejoras: {error?.message}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Gestión de Mejoras</h2>

        {/* Filtros unificados */}
        <UnifiedFilters
          filters={{
            ...filters,
            workflow_ids: filters.n8n_workflow_id || []
          }}
          onFilterChange={(key, value) => {
            if (key === 'workflow_ids') {
              handleFilterChange('n8n_workflow_id', value);
            } else {
              handleFilterChange(key, value);
            }
          }}
          onClearFilters={handleClearFilters}
          showEstado={true}
          showWorkflows={true}
          showEtiquetas={false}
          showSugerencias={false}
          showPuntuacion={false}
          showFechas={true}
          showAgente={true}
          estadoType="mejoras"
          title="Filtros de Mejoras"
        />

        {/* Botón de aplicar mejoras seleccionadas */}
        {selectedMejoras.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800 font-medium">
                  {selectedMejoras.length} mejora(s) seleccionada(s)
                </span>
              </div>
              <button
                onClick={handleApplySelected}
                disabled={isApplying}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isApplying ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Aplicando...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4" />
                    <span>Aplicar Mejoras</span>
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-500 mt-4">
          {total} mejoras encontradas
        </div>
      </div>

      {/* New List View */}
      <ImprovementsListView
        mejoras={mejoras}
        selectedIds={selectedMejoras}
        onToggleSelection={handleSelectMejora}
        onSelectAll={(checked) => {
          if (checked) {
            setSelectedMejoras(mejoras.map(m => m.id));
          } else {
            setSelectedMejoras([]);
          }
        }}
        onClearSelection={() => setSelectedMejoras([])}
        onViewDetails={handleViewDetails}
        onApply={handleApplyMejora}
        onDiscard={handleDiscardMejora}
        isLoading={isLoading}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
        groupBy={groupBy}
        onGroupByChange={setGroupBy}
      />

      {/* Pagination */}
      {!isLoading && totalPages > 1 && (
        <div className="flex items-center justify-between bg-white px-4 py-3 border rounded-lg">
          <div className="text-sm text-gray-700">
            Mostrando {((filters.page - 1) * filters.page_size) + 1} a {Math.min(filters.page * filters.page_size, total)} de {total} resultados
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => updateFilters({ page: Math.max(filters.page - 1, 1) })}
              disabled={filters.page === 1}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Anterior
            </button>
            <span className="px-3 py-1 text-sm bg-blue-50 text-blue-600 border rounded">
              {filters.page} de {totalPages}
            </span>
            <button
              onClick={() => updateFilters({ page: Math.min(filters.page + 1, totalPages) })}
              disabled={filters.page === totalPages}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Siguiente
            </button>
          </div>
        </div>
      )}

      {/* Full Screen Diff Viewer */}
      {showDiffViewer && selectedMejoraDetail && (
        <FullScreenDiffViewer
          mejora={selectedMejoraDetail}
          isOpen={showDiffViewer}
          onClose={handleCloseDiffViewer}
          onApply={handleApplyMejora}
          onDiscard={handleDiscardMejora}
          isApplying={isApplying}
          isDiscarding={isDiscarding}
        />
      )}
    </div>
  );
};
