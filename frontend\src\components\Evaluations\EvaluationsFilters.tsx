import React from 'react';
import { UnifiedFilters } from './UnifiedFilters';
import { EvaluacionFilters } from '../../types/evaluaciones';

type FilterValue = string | string[] | number | boolean | null | undefined;

interface EvaluationsFiltersProps {
  filters: EvaluacionFilters;
  onFilterChange: (key: string, value: FilterValue) => void;
  onClearFilters: () => void;
}

export const EvaluationsFilters: React.FC<EvaluationsFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters
}) => {
  return (
    <UnifiedFilters
      filters={filters}
      onFilterChange={onFilterChange}
      onClearFilters={onClearFilters}
      showEstado={true}
      showWorkflows={true}
      showEtiquetas={true}
      showSugerencias={true}
      showPuntuacion={true}
      showFechas={true}
      showAgente={false}
      estadoType="evaluaciones"
      title="Filtros"
    />
  );
};


