import React, { useState, useMemo } from 'react';
import { Filter, X, Search, ChevronDown, ChevronUp } from 'lucide-react';
import { EstadoEvaluacion, EstadoMejoraAgente } from '../../types/evaluaciones';
import { useWorkflowsForFilters, useEtiquetasForFilters } from '../../hooks/useEvaluations';

// Definir tipos específicos para los filtros
export type FilterValue = string | string[] | number | boolean | null | undefined;
export type FiltersRecord = Record<string, FilterValue>;

interface UnifiedFiltersProps {
  filters: FiltersRecord;
  onFilterChange: (key: string, value: FilterValue) => void;
  onClearFilters: () => void;
  // Configuración de qué filtros mostrar
  showEstado?: boolean;
  showWorkflows?: boolean;
  showEtiquetas?: boolean;
  showSugerencias?: boolean;
  showPuntuacion?: boolean;
  showFechas?: boolean;
  showAgente?: boolean;
  // Tipo de estado para usar (evaluaciones o mejoras)
  estadoType?: 'evaluaciones' | 'mejoras';
  // Título personalizado
  title?: string;
}

export const UnifiedFilters: React.FC<UnifiedFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  showEstado = true,
  showWorkflows = true,
  showEtiquetas = true,
  showSugerencias = false,
  showPuntuacion = false,
  showFechas = false,
  showAgente = false,
  estadoType = 'evaluaciones',
  title = 'Filtros'
}) => {
  const { data: workflows, isLoading: isLoadingWorkflows } = useWorkflowsForFilters();
  const { data: etiquetas, isLoading: isLoadingEtiquetas } = useEtiquetasForFilters();

  // Estados para colapsar secciones
  const [workflowsCollapsed, setWorkflowsCollapsed] = useState(true);
  const [etiquetasCollapsed, setEtiquetasCollapsed] = useState(true);

  // Estado para búsqueda de workflows
  const [workflowSearch, setWorkflowSearch] = useState('');

  const estadoOptions = useMemo(() => {
    if (estadoType === 'mejoras') {
      return [
        { value: EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA, label: 'Pendiente Revisión' },
        { value: EstadoMejoraAgente.APROBADO_PARA_APLICAR, label: 'Aprobado para Aplicar' },
        { value: EstadoMejoraAgente.DESCARTADO, label: 'Descartado' },
        { value: EstadoMejoraAgente.APLICADO, label: 'Aplicado' }
      ];
    } else {
      return [
        { value: EstadoEvaluacion.PENDIENTE_REVISION, label: 'Pendiente Revisión' },
        { value: EstadoEvaluacion.REVISADO, label: 'Revisado' },
        { value: EstadoEvaluacion.PENDIENTE_MEJORAS, label: 'Pendiente Mejoras' },
        { value: EstadoEvaluacion.MEJORAS_APLICADAS, label: 'Mejoras Aplicadas' }
      ];
    }
  }, [estadoType]);

  // Filtrar workflows por búsqueda
  const filteredWorkflows = useMemo(() => {
    if (!workflows) return [];
    if (!workflowSearch.trim()) return workflows;

    return workflows.filter(workflow =>
      workflow.nombre.toLowerCase().includes(workflowSearch.toLowerCase())
    );
  }, [workflows, workflowSearch]);

  const handleEstadoChange = (estado: EstadoEvaluacion | EstadoMejoraAgente, checked: boolean) => {
    const currentEstados = Array.isArray(filters.estado) ? filters.estado : [];
    const newEstados = checked
      ? [...currentEstados, estado]
      : currentEstados.filter((e: string) => e !== estado);

    onFilterChange('estado', newEstados);
  };

  const handleWorkflowChange = (workflowId: string, checked: boolean) => {
    const currentWorkflows = Array.isArray(filters.workflow_ids) ? filters.workflow_ids : [];
    const newWorkflows = checked
      ? [...currentWorkflows, workflowId]
      : currentWorkflows.filter((id: string) => id !== workflowId);

    onFilterChange('workflow_ids', newWorkflows);
  };

  const handleEtiquetaChange = (etiquetaId: string, checked: boolean) => {
    const currentEtiquetas = Array.isArray(filters.workflow_tags) ? filters.workflow_tags : [];
    const newEtiquetas = checked
      ? [...currentEtiquetas, etiquetaId]
      : currentEtiquetas.filter((id: string) => id !== etiquetaId);

    onFilterChange('workflow_tags', newEtiquetas);
  };

  const hasActiveFilters = () => {
    return (
      (Array.isArray(filters.estado) && filters.estado.length > 0) ||
      (Array.isArray(filters.workflow_ids) && filters.workflow_ids.length > 0) ||
      (Array.isArray(filters.workflow_tags) && filters.workflow_tags.length > 0) ||
      filters.solo_con_sugerencias ||
      filters.puntuacion_min ||
      filters.puntuacion_max ||
      filters.fecha_desde ||
      filters.fecha_hasta ||
      (Array.isArray(filters.agente_id) && filters.agente_id.length > 0)
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        
        {hasActiveFilters() && (
          <button
            onClick={onClearFilters}
            className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="w-4 h-4" />
            <span>Limpiar filtros</span>
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Filtro de Estado */}
        {showEstado && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Estado</h4>
            <div className="space-y-2">
              {estadoOptions.map((option) => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={Array.isArray(filters.estado) && filters.estado.includes(option.value) || false}
                    onChange={(e) => handleEstadoChange(option.value, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Filtro de Solo con Sugerencias */}
        {showSugerencias && (
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={Boolean(filters.solo_con_sugerencias)}
                onChange={(e) => onFilterChange('solo_con_sugerencias', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Solo con sugerencias</span>
            </label>
          </div>
        )}

        {/* Filtro de Workflows */}
        {showWorkflows && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900">Workflows</h4>
              <button
                onClick={() => setWorkflowsCollapsed(!workflowsCollapsed)}
                className="text-gray-400 hover:text-gray-600"
              >
                {workflowsCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>
            </div>
            
            {!workflowsCollapsed && (
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar workflows..."
                    value={workflowSearch}
                    onChange={(e) => setWorkflowSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  />
                </div>
                
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {isLoadingWorkflows ? (
                    <div className="text-sm text-gray-500">Cargando workflows...</div>
                  ) : filteredWorkflows.length === 0 ? (
                    <div className="text-sm text-gray-500">No se encontraron workflows</div>
                  ) : (
                    filteredWorkflows.map((workflow) => (
                      <label key={workflow.id} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={Array.isArray(filters.workflow_ids) && filters.workflow_ids.includes(workflow.id) || false}
                          onChange={(e) => handleWorkflowChange(workflow.id, e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700 truncate">{workflow.nombre}</span>
                      </label>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Filtro de Etiquetas */}
        {showEtiquetas && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900">Etiquetas</h4>
              <button
                onClick={() => setEtiquetasCollapsed(!etiquetasCollapsed)}
                className="text-gray-400 hover:text-gray-600"
              >
                {etiquetasCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>
            </div>

            {!etiquetasCollapsed && (
              <div className="max-h-40 overflow-y-auto space-y-2">
                {isLoadingEtiquetas ? (
                  <div className="text-sm text-gray-500">Cargando etiquetas...</div>
                ) : !etiquetas || etiquetas.length === 0 ? (
                  <div className="text-sm text-gray-500">No se encontraron etiquetas</div>
                ) : (
                  etiquetas.map((etiqueta) => (
                    <label key={etiqueta.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={Array.isArray(filters.workflow_tags) && filters.workflow_tags.includes(etiqueta.id) || false}
                        onChange={(e) => handleEtiquetaChange(etiqueta.id, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">{etiqueta.name}</span>
                    </label>
                  ))
                )}
              </div>
            )}
          </div>
        )}

        {/* Filtro de Agente */}
        {showAgente && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Agente</h4>
            <input
              type="text"
              value={Array.isArray(filters.agente_id) ? filters.agente_id.join(',') : ''}
              onChange={(e) => {
                const value = e.target.value.trim();
                if (!value) {
                  onFilterChange('agente_id', []);
                } else {
                  // Mejorar el parsing: dividir por comas y limpiar espacios
                  const agentIds = value
                    .split(',')
                    .map(id => id.trim())
                    .filter(id => id.length > 0);
                  onFilterChange('agente_id', agentIds);
                }
              }}
              placeholder="Nombre del agente (separar múltiples con comas)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
          </div>
        )}

        {/* Filtro de Puntuación */}
        {showPuntuacion && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Puntuación</h4>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Mínima</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={typeof filters.puntuacion_min === 'number' ? filters.puntuacion_min : ''}
                  onChange={(e) => onFilterChange('puntuacion_min', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Máxima</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={typeof filters.puntuacion_max === 'number' ? filters.puntuacion_max : ''}
                  onChange={(e) => onFilterChange('puntuacion_max', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
            </div>
          </div>
        )}

        {/* Filtro de Fechas */}
        {showFechas && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Fechas</h4>
            <div className="grid grid-cols-1 gap-3">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Desde</label>
                <input
                  type="date"
                  value={typeof filters.fecha_desde === 'string' ? filters.fecha_desde : ''}
                  onChange={(e) => onFilterChange('fecha_desde', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Hasta</label>
                <input
                  type="date"
                  value={typeof filters.fecha_hasta === 'string' ? filters.fecha_hasta : ''}
                  onChange={(e) => onFilterChange('fecha_hasta', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
